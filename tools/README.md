# Auspex Records Label Tools

This directory contains automation tools for processing and managing music releases for Auspex Records.

## [`convert_s3_codecs.py`](convert_s3_codecs.py)

### Overview

An automated audio processing pipeline that converts high-quality WAV files into multiple audio formats and makes them available for download on the Auspex Records website. The script handles the entire workflow from raw audio files to web-ready download packages.

### How It Works

The script operates as a **differential processing system** that:

1. **Compares S3 buckets** to identify new releases that need processing
2. **Downloads raw audio files** from the preprocessing bucket
3. **Converts audio** to multiple formats using FFmpeg
4. **Packages releases** into format-specific ZIP files
5. **Uploads processed releases** to the public releases bucket

### Input Requirements

#### S3 Bucket Structure

**Preprocessing Bucket (`auspex-records-preprocess`):**
```
auspex-records-preprocess/
├── Artist Name - Album Title/
│   ├── 01 - Track Name.wav          # High-quality WAV files (required)
│   ├── 02 - Another Track.wav
│   ├── cover.jpg                    # Album artwork (optional)
│   └── liner-notes.pdf              # Additional files (optional)
└── Another Artist - Another Album/
    ├── 01 - Song One.wav
    └── cover.png
```

#### File Requirements

- **Audio Files**: Must be in WAV format (`.wav` extension)
- **Quality**: Recommend 24-bit/48kHz or higher for best results
- **Naming**: Use consistent naming convention: `## - Track Name.wav`
- **Additional Files**: Cover art, liner notes, etc. will be copied to all format folders

### Output Structure

**Releases Bucket (`auspex-records-releases`):**
```
auspex-records-releases/
└── Artist Name - Album Title/
    ├── Artist Name - Album Title - MP3 320.zip
    ├── Artist Name - Album Title - MP3 V0.zip
    ├── Artist Name - Album Title - FLAC.zip
    ├── Artist Name - Album Title - AAC.zip
    ├── Artist Name - Album Title - Ogg Vorbis.zip
    ├── Artist Name - Album Title - ALAC.zip
    ├── Artist Name - Album Title - WAV.zip
    └── Artist Name - Album Title - AIFF.zip
```

Each ZIP file contains:
- All tracks in the specified format
- Cover art and additional files (copied from input)
- Proper file extensions for each format

### Audio Formats & Quality Settings

| Format | Quality Setting | File Extension | Use Case |
|--------|----------------|----------------|----------|
| **MP3 320** | 320 kbps CBR | `.mp3` | High-quality lossy, universal compatibility |
| **MP3 V0** | Variable bitrate | `.mp3` | Efficient lossy, smaller file sizes |
| **FLAC** | Lossless | `.flac` | Audiophile quality, open source |
| **AAC** | VBR quality 0 | `.aac` | Apple ecosystem, efficient compression |
| **Ogg Vorbis** | VBR quality 0 | `.oga` | Open source alternative to MP3 |
| **ALAC** | Lossless | `.m4a` | Apple lossless format |
| **WAV** | Uncompressed | `.wav` | Original quality, universal |
| **AIFF** | Uncompressed | `.aiff` | Apple uncompressed format |

### Prerequisites & Setup

#### System Requirements
- **Python 3.12+**
- **FFmpeg** (with all codec support)
- **AWS CLI** configured with appropriate permissions
- **Sufficient disk space** for temporary processing (estimate 2-3GB per album)

#### Installation Steps

1. **Install Python dependencies:**
   ```bash
   pip install boto3
   ```

2. **Install FFmpeg:**
   ```bash
   # macOS
   brew install ffmpeg

   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg

   # Windows
   # Download from https://ffmpeg.org/download.html
   ```

3. **Configure AWS credentials:**
   ```bash
   aws configure
   # Enter your AWS Access Key ID, Secret Access Key, and region
   ```

4. **Create S3 buckets:**
   ```bash
   aws s3 mb s3://auspex-records-preprocess
   aws s3 mb s3://auspex-records-releases
   ```

#### Required AWS Permissions

Your AWS user/role needs the following permissions:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket",
                "s3:GetObject",
                "s3:PutObject"
            ],
            "Resource": [
                "arn:aws:s3:::auspex-records-preprocess",
                "arn:aws:s3:::auspex-records-preprocess/*",
                "arn:aws:s3:::auspex-records-releases",
                "arn:aws:s3:::auspex-records-releases/*"
            ]
        }
    ]
}

### Usage

#### Basic Usage
```bash
cd tools
python convert_s3_codecs.py
```

#### What Happens When You Run It

1. **Bucket Comparison**: Script compares preprocessing and releases buckets
2. **Progress Output**: Shows which albums will be processed
3. **Download Phase**: Downloads new albums from preprocessing bucket
4. **Conversion Phase**: Converts each WAV file to all 8 formats (parallel processing)
5. **Packaging Phase**: Creates ZIP files for each format
6. **Upload Phase**: Uploads all ZIP files to releases bucket
7. **Cleanup**: Removes temporary local files

#### Example Output
```
Directories to process: [
  "Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/",
  "Oak Project - Reflections/"
]
Processing: Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/
Downloading Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/01 - Its all how you look at it.wav...
Downloading Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/cover.jpg...
Converting 01 - Its all how you look at it...
Converting 02 - Lentamente...
... 01 - Its all how you look at it done.
... 02 - Lentamente done.
Uploading tmp/convert_s3_codecs/output/Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/Paranoiac - The Pots Of My Heart Are Full Of Your Seeds - MP3 320.zip...
Uploading tmp/convert_s3_codecs/output/Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/Paranoiac - The Pots Of My Heart Are Full Of Your Seeds - FLAC.zip...
...
```

### Workflow Integration

#### For New Releases

1. **Prepare Audio Files**:
   - Master your tracks to high-quality WAV files
   - Use consistent naming: `01 - Track Name.wav`
   - Include cover art and any additional files

2. **Upload to Preprocessing Bucket**:
   ```bash
   aws s3 sync "local/album/folder" s3://auspex-records-preprocess/"Artist - Album Title"/
   ```

3. **Run Processing Script**:
   ```bash
   python convert_s3_codecs.py
   ```

4. **Verify Results**:
   - Check that all ZIP files were created in releases bucket
   - Test download links on website
   - Verify audio quality of converted files

#### Integration with Website

The processed files integrate directly with the Auspex Records website:

- **Download URLs**: `https://auspex-records-releases.s3.us-west-1.amazonaws.com/{Artist}+-+{Album}/{Artist}+-+{Album}+-+{Format}.zip`
- **Format Selection**: Website download modal uses these exact format names
- **Automatic Discovery**: New releases appear automatically once ZIP files are uploaded

### Performance & Optimization

#### Processing Time
- **Per Track**: ~2-3 minutes for all 8 formats (parallel processing)
- **Per Album**: Depends on track count (4 tracks ≈ 8-12 minutes)
- **Bottlenecks**: FFmpeg conversion, S3 upload bandwidth

#### Resource Usage
- **CPU**: High during conversion (uses all available cores)
- **Memory**: ~500MB-1GB during processing
- **Disk**: 2-3GB temporary space per album
- **Network**: Upload bandwidth dependent

#### Optimization Tips
- **Run during off-peak hours** for faster S3 transfers
- **Ensure sufficient disk space** before processing large batches
- **Monitor AWS costs** - S3 transfer and storage fees apply
- **Use SSD storage** for faster temporary file operations

### Troubleshooting

#### Common Issues

**"No such file or directory" errors:**
- Ensure AWS credentials are configured: `aws configure list`
- Verify bucket names and permissions
- Check that buckets exist: `aws s3 ls`

**FFmpeg conversion failures:**
- Verify FFmpeg installation: `ffmpeg -version`
- Check input WAV file integrity
- Ensure sufficient disk space

**S3 upload failures:**
- Check AWS credentials and permissions
- Verify internet connectivity
- Monitor AWS service status

**Empty processing list:**
- Confirms no new albums need processing
- Check preprocessing bucket contents: `aws s3 ls s3://auspex-records-preprocess/`
- Verify album folder structure matches expected format

#### Debug Mode
Add debug output by modifying the script:
```python
# Add at top of main() function
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Configuration

#### Customizing Bucket Names
Edit the constants at the top of `convert_s3_codecs.py`:
```python
S3_PREPROCESS_BUCKET = "your-preprocess-bucket"
S3_RELEASES_BUCKET = "your-releases-bucket"
```

#### Adding New Audio Formats
To add additional formats, modify the `convert_commands` list in the `convert_file()` function:
```python
# Example: Add OGG format
[
    f"ffmpeg -i \"{input_path}\" -codec:a libvorbis -q:a 5 \"{output_path}.ogg\"",
    "OGG"
]
```

#### Adjusting Quality Settings
Modify the FFmpeg parameters in `convert_commands`:
- **MP3 bitrate**: Change `-b:a 320k` to desired bitrate
- **FLAC compression**: Add `-compression_level 8` for maximum compression
- **AAC quality**: Adjust `-q:a 0` (0=highest, 5=lowest)

### Security Considerations

- **AWS Credentials**: Never commit credentials to version control
- **Bucket Permissions**: Use least-privilege access policies
- **Public Access**: Releases bucket should allow public read access for downloads
- **Preprocessing Bucket**: Should be private, only accessible by processing script

### Monitoring & Maintenance

#### Regular Tasks
- **Monitor S3 costs** and usage patterns
- **Clean up old preprocessing files** after successful processing
- **Verify backup procedures** for both buckets
- **Update FFmpeg** periodically for security and performance

#### Automation Opportunities
- **Scheduled Processing**: Run script via cron/scheduled task
- **Webhook Integration**: Trigger processing on new uploads
- **Notification System**: Alert on processing completion/failures
- **Quality Assurance**: Automated audio quality verification
```