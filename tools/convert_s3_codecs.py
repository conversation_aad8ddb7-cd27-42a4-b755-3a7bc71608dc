"""
Auspex Records - Releases Audio Codec Converter

Usage: python convert_s3_codecs.py
"""


import boto3
import json
import os
import subprocess
import zipfile

from shutil import copyfile, rmtree
from threading import Thread


LOCAL_INPUT_DIRECTORY = "tmp/convert_s3_codecs/input"
LOCAL_OUTPUT_DIRECTORY = "tmp/convert_s3_codecs/output"

S3_PREPROCESS_BUCKET = "auspex-records-preprocess"
S3_RELEASES_BUCKET = "auspex-records-releases"


def run_command(command: str):
    process = subprocess.Popen(command,
                               shell=True,
                               stdin=subprocess.DEVNULL,
                               stdout=subprocess.DEVNULL,
                               stderr=subprocess.DEVNULL)
    process.wait()


def zip_folder(folder_path, output_path):
    """Zips the contents of a folder.

    Args:
        folder_path (str): Path to the folder to zip.
        output_path (str): Path to the output zip file.
    """

    with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                out_name = os.path.relpath(file_path, folder_path)  # Preserve folder structure
                zipf.write(file_path, out_name)


def convert_file(input_file: str, path: str):
    """
    Converts the input_file to all required formats, each in its own directory inside of OUTPUT_DIRECTORY
    """

    convert_commands: list[list[str]] = [
        # MP3 320
        [
            f"ffmpeg -i \"{LOCAL_INPUT_DIRECTORY}/{path}/{input_file}.wav\" "
            f"-write_id3v2 1 -codec:a libmp3lame -b:a 320k \"{LOCAL_OUTPUT_DIRECTORY}/{path}/MP3 320/{input_file}.mp3\"",
            "MP3 320"
        ],
        # MP3 V0
        [
            f"ffmpeg -i \"{LOCAL_INPUT_DIRECTORY}/{path}/{input_file}.wav\" "
            f"-write_id3v2 1 -codec:a libmp3lame -q:a 0 \"{LOCAL_OUTPUT_DIRECTORY}/{path}/MP3 V0/{input_file}.mp3\"",
            "MP3 V0"
        ],
        # FLAC
        [
            f"ffmpeg -i \"{LOCAL_INPUT_DIRECTORY}/{path}/{input_file}.wav\" "
            f"-write_id3v2 1 -codec:a flac \"{LOCAL_OUTPUT_DIRECTORY}/{path}/FLAC/{input_file}.flac\"",
            "FLAC"
        ],
        # AAC
        [
            f"ffmpeg -i \"{LOCAL_INPUT_DIRECTORY}/{path}/{input_file}.wav\" "
            f"-write_id3v2 1 -codec:a aac -q:a 0 \"{LOCAL_OUTPUT_DIRECTORY}/{path}/AAC/{input_file}.aac\"",
            "AAC"
        ],
        # Ogg Vorbis
        [
            f"ffmpeg -i \"{LOCAL_INPUT_DIRECTORY}/{path}/{input_file}.wav\" "
            f"-write_id3v2 1 -codec:a libvorbis -q:a 0 \"{LOCAL_OUTPUT_DIRECTORY}/{path}/Ogg Vorbis/{input_file}.oga\"",
            "Ogg Vorbis"
        ],
        # ALAC
        [
            f"ffmpeg -i \"{LOCAL_INPUT_DIRECTORY}/{path}/{input_file}.wav\" "
            f"-write_id3v2 1 -codec:a alac -q:a 0 \"{LOCAL_OUTPUT_DIRECTORY}/{path}/ALAC/{input_file}.m4a\"",
            "ALAC"
        ],
        # WAV
        [
            f"cp \"{LOCAL_INPUT_DIRECTORY}/{path}/{input_file}.wav\" "
            f"\"{LOCAL_OUTPUT_DIRECTORY}/{path}/WAV/{input_file}.wav\"",
            "WAV"
        ],
        # AIFF
        [
            f"ffmpeg -i \"{LOCAL_INPUT_DIRECTORY}/{path}/{input_file}.wav\" "
            f"-write_id3v2 1 -c:v copy \"{LOCAL_OUTPUT_DIRECTORY}/{path}/AIFF/{input_file}.aiff\"",
            "AIFF"
        ],
    ]

    codec_threads: list[Thread] = []
    for command, codec in convert_commands:
        try:
            os.makedirs(f"{LOCAL_OUTPUT_DIRECTORY}/{path}/{codec}")
        except FileExistsError:
            # Already created when processing a previous track
            pass

        codec_thread = Thread(target=run_command, args=(command,))
        codec_threads.append(codec_thread)
        codec_thread.start()

    for codec_thread in codec_threads:
        codec_thread.join()
    print(f"... {input_file} done.")


def main():
    s3_client = boto3.client("s3")

    # Check S3 buckets for albums that haven't been processed yet
    try:
        preprocess_directories_response = s3_client.list_objects(
            Bucket=S3_PREPROCESS_BUCKET, Delimiter='/'
        )
        common_prefixes = preprocess_directories_response.get("CommonPrefixes")
        if common_prefixes:
            preprocess_directories = list(content["Prefix"] for content in common_prefixes)
        else:
            preprocess_directories = []
    except Exception as e:
        print(f"Error accessing preprocess bucket '{S3_PREPROCESS_BUCKET}': {e}")
        print("Make sure the bucket exists and you have proper AWS credentials configured.")
        return

    try:
        releases_directories_response = s3_client.list_objects(
            Bucket=S3_RELEASES_BUCKET, Delimiter='/'
        )
        common_prefixes = releases_directories_response.get("CommonPrefixes")
        if common_prefixes:
            releases_directories = list(content["Prefix"] for content in common_prefixes)
        else:
            releases_directories = []
    except Exception as e:
        print(f"Error accessing releases bucket '{S3_RELEASES_BUCKET}': {e}")
        print("Make sure the bucket exists and you have proper AWS credentials configured.")
        return

    directories_to_process = list(
        directory for directory in preprocess_directories if directory not in releases_directories
    )
    print(f"Directories to process: {json.dumps(directories_to_process, indent=2)}")

    # Empty local input and output directories before converting
    for local_directory in (LOCAL_INPUT_DIRECTORY, LOCAL_OUTPUT_DIRECTORY):
        try:
            rmtree(local_directory)
        except FileNotFoundError:
            # Already deleted
            pass

    # Process each missing release
    for directory_to_process in directories_to_process:
        print(f"Processing: {directory_to_process}")
        input_path = f"{LOCAL_INPUT_DIRECTORY}/{directory_to_process}"
        output_path = f"{LOCAL_OUTPUT_DIRECTORY}/{directory_to_process}"

        # Create local input and output directories
        for local_directory in (input_path, output_path):
            os.makedirs(local_directory)

        # Copy album locally
        files_response = s3_client.list_objects(Bucket=S3_PREPROCESS_BUCKET, Prefix=directory_to_process)
        for file in files_response.get("Contents", []):
            # Skip if the key ends with a forward slash (directory marker)
            if file["Key"].endswith('/'):
                continue
                
            # Create the local directory structure if it doesn't exist
            local_file_path = f"{LOCAL_INPUT_DIRECTORY}/{file['Key']}"
            os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
            
            # Download the file
            s3_client.download_file(S3_PREPROCESS_BUCKET, file["Key"], local_file_path)

        # Convert files to individual audio codecs
        extra_files = []
        files = os.listdir(input_path)
        file_threads: list[Thread] = []
        for file in files:
            if file.endswith(".wav"):
                # Trim .wav extension
                filename = file[:file.rfind(".")]
                print(f"Converting {filename}...\n")
                file_thread = Thread(target=convert_file, args=(filename, directory_to_process))
                file_threads.append(file_thread)
                file_thread.start()
            else:
                extra_files.append(file)

        for file_thread in file_threads:
            file_thread.join()

        # Copy any extra non-WAV files in the folder (i.e. cover art) to the codec folders
        for file in extra_files:
            for codec_directory in os.listdir(output_path):
                copyfile(
                    os.path.join(input_path, file),
                    os.path.join(output_path, codec_directory, file)
                )

    # Zip codec folders and upload them to S3 releases bucket
    if os.path.exists(LOCAL_OUTPUT_DIRECTORY) and os.listdir(LOCAL_OUTPUT_DIRECTORY):
        for album_directory in os.listdir(LOCAL_OUTPUT_DIRECTORY):
            album_path = os.path.join(LOCAL_OUTPUT_DIRECTORY, album_directory)
            for codec_directory in os.listdir(album_path):
                codec_path = os.path.join(album_path, codec_directory)
                zip_path = f"{album_path}/{album_directory} - {codec_directory}.zip"
                zip_folder(codec_path, zip_path)
                print(f"Uploading {zip_path}...")
                s3_client.upload_file(zip_path,
                                      S3_RELEASES_BUCKET,
                                      f"{album_directory}/{album_directory} - {codec_directory}.zip")
    else:
        print("No albums were processed, skipping upload step.")


if __name__ == "__main__":
    main()
